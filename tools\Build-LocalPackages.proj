<Project DefaultTargets="BuildPackages">
  <Import Project="..\Directory.Build.props" />

  <PropertyGroup>
    <Configuration Condition="'$(Configuration)' == ''">Debug</Configuration>
  </PropertyGroup>

  <!-- List of projects that produce NuGet packages -->
  <ItemGroup>
    <PackableProject Include="..\src\PreviewFramework\PreviewFramework.csproj" />
    <PackableProject Include="..\src\tooling\PreviewFramework.DevTools\PreviewFramework.DevTools.csproj" />
    <PackableProject Include="..\src\platforms\PreviewFramework.App.Maui\PreviewFramework.App.Maui.csproj" />
    <PackableProject Include="..\src\platforms\PreviewFramework.App.Wpf\PreviewFramework.App.Wpf.csproj" />
  </ItemGroup>

  <!-- Main target that builds packages -->
  <Target Name="BuildPackages">
    <Message Text="Building all NuGet packages in $(Configuration) configuration..." Importance="high" />

    <!-- Build AppBuildTasks first as it's needed by SharedModel -->
    <MSBuild Projects="..\src\PreviewFramework.AppBuildTasks\PreviewFramework.AppBuildTasks.csproj"
             Targets="Build"
             Properties="Configuration=$(Configuration)" />

    <!-- Also explicitly build PreviewFramework.DevToolsApp, which is included in PreviewFramework.DevTools -->
    <MSBuild Projects="..\src\tooling\PreviewFramework.DevToolsApp\PreviewFramework.DevToolsApp.csproj"
             Targets="Build"
             Properties="Configuration=$(Configuration)" />

    <!-- Pack all projects -->
    <MSBuild Projects="@(PackableProject)"
             Targets="Pack"
             Properties="Configuration=$(Configuration)"
             ContinueOnError="false" />

    <Message Text="Packages built to: $(PackageOutputPath)" Importance="high" />
  </Target>

  <!-- Alias common target names to BuildPackages -->
  <Target Name="Build" DependsOnTargets="BuildPackages" />
  <Target Name="Pack" DependsOnTargets="BuildPackages" />
</Project>
