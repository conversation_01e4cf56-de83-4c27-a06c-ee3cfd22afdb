<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>mcp-server-mobiledev</ToolCommandName>
  </PropertyGroup>

  <PropertyGroup>
    <PackageId>Mcp.Server.MobileDev</PackageId>
    <Title>Mobile Development MCP (Model Context Protocol) Server</Title>
    <PackageDescription>This is a MCP designed to manage and interact with mobile devices and simulators</PackageDescription>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.3" />
    <PackageReference Include="ModelContextProtocol" Version="0.1.0-preview.4" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.3" />
  </ItemGroup>

</Project>
