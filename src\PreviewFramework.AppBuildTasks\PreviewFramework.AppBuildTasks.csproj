<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <Nullable>enable</Nullable>
    <EnableDynamicLoading>true</EnableDynamicLoading>
    <Description>MSBuild tasks for PreviewFramework</Description>

    <!-- To avoid warning, disable dynamic platform resolution since this is referenced with SkipGetTargetFrameworkProperties="true" -->
    <EnableDynamicPlatformResolution>false</EnableDynamicPlatformResolution>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Build.Framework" Version="17.14.8" />
    <PackageReference Include="Microsoft.Build.Utilities.Core" Version="17.14.8" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

</Project>
