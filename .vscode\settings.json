{
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "omnisharp.enableEditorConfigSupport": true,
  "omnisharp.enableRoslynAnalyzers": true,
  "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
  "editor.formatOnSave": true,
  "[xml]": {
    "editor.wordWrap": "off"
  },
  // Treat these files as Azure Pipelines files
  "files.associations": {
    "**/azure-pipelines/**/*.yml": "azure-pipelines",
    "azure-pipelines.yml": "azure-pipelines"
  },
  // Use Prettier as the default formatter for Azure Pipelines files.
  // Needs to be explicitly configured: https://github.com/Microsoft/azure-pipelines-vscode#document-formatting
  "[azure-pipelines]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": false // enable this when they conform
  },
  "dotnet.defaultSolution": "PreviewFramework.sln",

  // Disable the git pager in the integrated terminal to avoid it being invoked for AI agent commands, like from Cursor
  "terminal.integrated.env.windows": {
    "GIT_PAGER": ""
  },
  "terminal.integrated.env.osx": {
    "GIT_PAGER": ""
  },
  "terminal.integrated.env.linux": {
    "GIT_PAGER": ""
  }
}
