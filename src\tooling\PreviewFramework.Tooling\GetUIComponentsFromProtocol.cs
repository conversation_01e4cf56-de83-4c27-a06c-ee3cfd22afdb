using PreviewFramework.SharedModel;
using PreviewFramework.SharedModel.Protocol;

namespace PreviewFramework.Tooling;

public class GetUIComponentsFromProtocol : UIComponentsManagerBuilderTooling
{
    /// <summary>
    /// Initializes a new instance of GetUI<PERSON>om<PERSON><PERSON>romApp and processes the UI component information from the app,
    /// creating and organizing UI components based on the provided data.
    /// </summary>
    /// <param name="uiComponentInfos">Array of UI component information from the protocol</param>
    public GetUIComponentsFromProtocol(UIComponentInfo[] uiComponentInfos)
    {
        foreach (UIComponentInfo uiComponentInfo in uiComponentInfos)
        {
            // Create the UI component and add it to the builder
            UIComponentKind componentKind = UIComponentKindInfo.ToUIComponentKind(uiComponentInfo.UIComponentKindInfo);
            var uiComponent = new UIComponentTooling(componentKind, uiComponentInfo.Name, uiComponentInfo.DisplayName,
                uiComponentInfo.Previews.Select(CreatePreview).ToList());

            // Add the component to the builder
            AddOrUpdateUIComponent(uiComponent);
        }
    }



    /// <summary>
    /// Creates a PreviewTooling object from protocol PreviewInfo.
    /// </summary>
    /// <param name="previewInfo">Preview information from the protocol</param>
    /// <returns>A PreviewTooling object</returns>
    private static PreviewTooling CreatePreview(PreviewInfo previewInfo)
    {
        return previewInfo.PreviewType switch
        {
            PreviewTypeInfo.Class => new PreviewClassTooling(previewInfo.Name, previewInfo.DisplayName, previewInfo.IsAutoGenerated),
            PreviewTypeInfo.StaticMethod => new PreviewStaticMethodTooling(previewInfo.Name, previewInfo.DisplayName),
            _ => throw new NotImplementedException($"Unknown preview type: {previewInfo.PreviewType}")
        };
    }
}
