<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
    <PackageReference Include="StreamJsonRpc" Version="2.22.11" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PreviewFramework\PreviewFramework.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Include the AppBuildTasks assembly and associated targets file in the NuGet package, so the platform NuGets can use the task -->
    <None Include="..\..\bin\PreviewFramework.AppBuildTasks\$(Configuration)\netstandard2.0\PreviewFramework.AppBuildTasks.dll" Pack="true" PackagePath="buildTransitive\PreviewFramework.AppBuildTasks.dll" />
    <None Include="buildTransitive\PreviewFramework.SharedModel.targets" Pack="true" PackagePath="buildTransitive\PreviewFramework.SharedModel.targets" />
  </ItemGroup>

</Project>
