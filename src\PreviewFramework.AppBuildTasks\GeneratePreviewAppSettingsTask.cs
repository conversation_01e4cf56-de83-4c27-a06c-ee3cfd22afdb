using System.Runtime.InteropServices;
using System.Text.Json;
using Microsoft.Build.Framework;

namespace PreviewFramework.AppBuildTasks
{
    public class GeneratePreviewAppSettingsTask : Microsoft.Build.Utilities.Task
    {
        [Required]
        public required string ProjectPath { get; set; }

        [Required]
        public required string OutputPath { get; set; }

        [Required]
        public required string PlatformPreviewApplication { get; set; }

        public override bool Execute()
        {
            try
            {
                string projectPath = Path.GetFullPath(ProjectPath);
                string outputPath = Path.GetFullPath(OutputPath);
                string? outputDirectory = Path.GetDirectoryName(outputPath);

                if (outputDirectory is not null && !Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                }

                // Get the connection string from the JSON file
                string connectionString = "";
                try
                {
                    string homeDir = Environment.GetFolderPath(
                        RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                            ? Environment.SpecialFolder.UserProfile
                            : Environment.SpecialFolder.Personal);
                    string configDir = Path.Combine(homeDir, ".previewframework");
                    string jsonPath = Path.Combine(configDir, "devToolsConnectionSettings.json");

                    if (File.Exists(jsonPath))
                    {
                        string jsonContent = File.ReadAllText(jsonPath);
                        using (JsonDocument doc = JsonDocument.Parse(jsonContent))
                        {
                            if (doc.RootElement.TryGetProperty("app", out var appElement) &&
                                appElement.ValueKind == JsonValueKind.String)
                            {
                                connectionString = appElement.GetString() ?? "";
                            }
                        }
                    }
                }
                catch
                {
                    // If any error occurs, use empty string as fallback
                    connectionString = "";
                }

                string content = $$"""
// <auto-generated/>
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace PreviewFramework.SharedModel
{
    public static class PreviewApplicationInitializer
    {
        [ModuleInitializer]
        public static void Initialize()
        {
            var previewApp = {{PlatformPreviewApplication}};
            if (previewApp != null)
            {
                previewApp.ProjectPath = @"{{projectPath.Replace("\"", "\"\"")}}";
                previewApp.ToolingConnectionString = @"{{connectionString.Replace("\"", "\"\"")}}";
                {{(connectionString != "" ? "previewApp.StartToolingConnection();" : "// No tooling connection, so not starting connection")}}
            }
        }
    }
}
""";

                File.WriteAllText(outputPath, content);
                return true;
            }
            catch (Exception ex)
            {
                Log.LogErrorFromException(ex);
                return false;
            }
        }
    }
}
